---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: grafana-vaultstore
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-apps-internal
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  labels:
    app.kubernetes.io/name: grafana-secrets
    app.kubernetes.io/part-of: grafana
  name: grafana-secrets
spec:
  secretStoreRef:
    name: grafana-vaultstore
    kind: SecretStore
  refreshInterval: "12h"
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
        labels:
          argocd.argoproj.io/secret-type: repo-creds
  data:
    - secretKey: LDAP_BIND_DN
      remoteRef:
        key: cobra/automation/accounts/jenkins_common
        property: LDAP_USER
    - secretKey: LDAP_BIND_PASSWORD
      remoteRef:
        key: cobra/automation/accounts/jenkins_common
        property: LDAP_PASSWORD
    - secretKey: CRITERION_LOKI_PASSWORD # username hard-coded as env var
      remoteRef:
        key: cobra/automation/azure/dre-criterion/observability/ingress-auth-plain
        property: loki
  dataFrom:
    - extract:
        key: cobra/automation/services/grafana
