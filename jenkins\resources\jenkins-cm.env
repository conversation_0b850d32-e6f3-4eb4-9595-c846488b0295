CASC_VAULT_TOKEN=no.token
CASC_VAULT_NAMESPACE=cds-dre-prod
CASC_VAULT_AGENT_ADDR=http://localhost:8200
VAULT_TOKEN=no.token
VAULT_ADDR=http://localhost:8200
VAULT_NAMESPACE=cds-dre-prod

JENKINS_MASTER_URL=https://aks-testing-jenkins.dre.ea.com/
JENKINS_MASTER_ADMIN_EMAIL=<EMAIL>
JENKINS_MASTER_MODE=EXCLUSIVE
JENKINS_MASTER_LABEL=master
PRODUCTION=true
DSL_BRANCH=master
JENKINS_SLAVE_AGENT_PORT=11308
NO_PLUGINS_REDEPLOY=true
JENKINS_URL=https://aks-testing-jenkins.dre.ea.com/

JENKINS_MASTER_LDAP_VAULT_PATH=cobra/automation/accounts/diceldap
JENKINS_MASTER_LDAP_DOMAINS=ad.ea.com;
JENKINS_MASTER_LDAP_SERVERS=ca1-ldap-vip.ad.ea.com:3268,va1-ldap-vip.ad.ea.com:3268
JENKINS_MASTER_LDAP_ADMIN_GROUPS=DRE.All
JENKINS_LOGSTASH_HOST=eac-drees-redis.eac.ad.ea.com
JENKINS_LOGSTASH_PORT=6379
JENKINS_LOGSTASH_KEY=jenkins_buildlogs_logstash
JENKINS_DEFAULT_VIEW=Self-Service


# We want to Jenkins to pull its k8s containers through artifactory's caches. 
JENKINS_OPTS=-Dorg.csanchez.jenkins.plugins.kubernetes.pipeline.PodTemplateStepExecution.dockerRegistryPrefix=docker.artifacts.ea.com

# We don't want to run the setup wizard because we programatically set things up.
# Disable DNS multicast because we're locked inside of kubernetes and
#   the multicast DNS queries don't work properly in it.
JAVA_OPTS=-Djenkins.install.runSetupWizard=false -Dhudson.DNSMultiCast.disabled=true -Dpermissive-script-security.enabled=no_security -Dhudson.model.User.allowNonExistentUserToLogin=true -XX:MaxRAMPercentage=80.0
#JAVA_OPTS="-Xmx12G -Xms12G -XX:MetaspaceSize=1024m -XX:MaxMetaspaceSize=2048m"
