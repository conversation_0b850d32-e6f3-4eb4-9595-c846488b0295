apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: jenkins
  annotations:
    # Use SSL
    traefik.ingress.kubernetes.io/router.entrypoints: web,websecure
    # Middlewares are the only thing that kustomize cannot modify for us, so we need to do it ourselves.
    # Before deploying these need to be updated to include the namespace where this jenkins is being deployed and 
    # the value of `namePrefix` from the kustomization.yaml if set, or the `<prefix>- ` value should be removed if not.
    traefik.ingress.kubernetes.io/router.middlewares: "cobra-jenkins-strip-gitmirror-prefix@kubernetescrd, traefik-https-redirect@kubernetescrd"
spec:
  tls:
    - hosts:
        - aks-testing-jenkins.dre.ea.com
  rules:
  - host: aks-testing-jenkins.dre.ea.com
    http:
      paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: jenkins-ui
              port:
                name: jenkins-http
        - path: /gm
          pathType: Prefix
          backend:
            service:
              name: jenkins-gitmirror
              port:
                name: gitmirror-http
