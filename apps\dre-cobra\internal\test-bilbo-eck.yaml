apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: test-bilbo-eck
  namespace: argocd
  labels:
    project: internal
    group: elasticsearch
spec:
  project: internal
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    path: ./apps/dre-cobra/internal/test-bilbo-eck
    targetRevision: master
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: internal
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
    # automated:
    #   prune: false
    #   selfHeal: false
