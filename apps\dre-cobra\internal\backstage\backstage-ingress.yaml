apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: backstage-ingress
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.middlewares: traefik-https-redirect@kubernetescrd
spec:
  tls:
    - hosts:
        - backstage.cobra.dre.ea.com
  rules:
    - host: backstage.cobra.dre.ea.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: backstage-svc
                port:
                  number: 80
