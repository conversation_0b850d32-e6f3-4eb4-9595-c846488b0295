apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: fb1-jenkins
  namespace: argocd
  labels:
    project: fb1
    group: jenkins
spec:
  project: fb1
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/helm-charts/jenkins.git
    path: "."
    targetRevision: 0.9.15
    helm:
      values: |
        instanceName: fb1
        createAgentListenerService: true
        serviceAccountName: fb1
        noPluginsRedeploy: true
        jenkins:
          image: dre-docker-federated.artifacts.ea.com/cobra/jenkins/jenkins-fb:2.479.3_fa3f874c
          javaOpts: "-XX:InitialRAMPercentage=50.0 -XX:MaxRAMPercentage=50.0"
          resources:
            requests:
              cpu: 500m
              memory: 16Gi
            limits:
              cpu: 4
              memory: 16Gi
          storageSize: 1000Gi
        elastic:
          url: https://dice-metrics-eck.cobra.dre.ea.com
          indexPrefix: fb
        vault:
          mountPath: kubernetes/cobra/azure/dre-stockholm
          role: cluster-apps-fb1
          mainAccountPath: cobra/automation/projects/fb1/accounts/main_account
          appRolePath: cobra/automation/projects/fb1/app_roles/jm-cobra-fb1
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: fb1
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
