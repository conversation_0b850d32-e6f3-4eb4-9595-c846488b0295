apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: dice-metrics-eck
  namespace: argocd
  labels:
    project: dice
    group: elasticsearch
spec:
  project: dice
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    path: ./apps/dre-cobra/dice/dice-metrics-eck
    targetRevision: master
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: dice
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
    # automated:
    #   prune: false
    #   selfHeal: false
