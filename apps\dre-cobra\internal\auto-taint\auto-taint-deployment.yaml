apiVersion: apps/v1
kind: Deployment
metadata:
  name: auto-taint
  labels:
    app: auto-taint
spec:
  replicas: 1
  selector:
    matchLabels:
      app: auto-taint
  template:
    metadata:
      labels:
        app: auto-taint
    spec:
      containers:
      - name: auto-taint
        image: dre-docker-federated.artifacts.ea.com/cobra/auto-taint:latest
        imagePullPolicy: "Always"
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "1Gi"
            cpu: "1"
        envFrom:
          - secretRef:
              name: auto-taint-secrets
