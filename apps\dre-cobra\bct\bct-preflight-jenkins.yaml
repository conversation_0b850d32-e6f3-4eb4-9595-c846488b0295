apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: bct-preflight-jenkins
  namespace: argocd
  labels:
    project: bct
    group: jenkins
spec:
  project: bct
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/helm-charts/jenkins.git
    path: "."
    targetRevision: 0.9.15
    helm:
      values: |
        instanceName: bct-preflight
        createAgentListenerService: true
        useOutsourcersIngress: true
        noPluginsRedeploy: true
        serviceAccountName: bct
        jenkins:
          image: dre-docker-federated.artifacts.ea.com/cobra/jenkins/jenkins-bct-preflight:2.479.3_fa3f874c
          javaOpts: "-XX:InitialRAMPercentage=50.0 -XX:MaxRAMPercentage=50.0"
          resources:
            requests:
              cpu: 500m
              memory: 16Gi
            limits:
              cpu: 4
              memory: 16Gi
          storageSize: 320Gi
        elastic:
          url: http://dice-metrics-eck-es-http.dice.svc.cluster.local:9200
          indexPrefix: bct
        vault:
          role: cluster-apps-bct
          mountPath: kubernetes/cobra/azure/dre-stockholm
          mainAccountPath: cobra/automation/projects/bct/accounts/main_account
          appRolePath: cobra/automation/projects/bct/app_roles/jm-bct
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: bct
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
  #   automated:
  #     prune: true
  #     selfHeal: true
