kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
  name: influxdb-ingress
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: "web,websecure"
    traefik.ingress.kubernetes.io/router.middlewares: "traefik-https-redirect@kubernetescrd"
spec:
  tls:
    - hosts:
        - influxdb.cobra.dre.ea.com
  rules:
    - host: influxdb.cobra.dre.ea.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: influxdb-svc
                port:
                  name: influxdb-port