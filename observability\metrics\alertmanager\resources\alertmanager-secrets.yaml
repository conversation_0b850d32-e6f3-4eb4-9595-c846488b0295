# We piggy back off the grafana 
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: alertmanager-main
  namespace: observability
spec:
  secretStoreRef:
    name: clustersetup-vaultstore
    kind: SecretStore
  refreshInterval: 720h # 30 days
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
  dataFrom:
    - extract:
        key: cobra/automation/kubernetes/azure/dre-stockholm/alertmanager
