[CmdletBinding()]
param (
    [Parameter()]
    [string]
    $kubeconfigPath,

    [Parameter()]
    [string]
    $VAULT_TOKEN = $env:VAULT_TOKEN,

    [Parameter()]
    [string]
    $VAULT_ADDR = $env:VAULT_ADDR,

    [Parameter()]
    [string]
    $VAULT_NAMESPACE = $env:VAULT_NAMESPACE,

    [Parameter()]
    [Switch]
    $base64Secret
)

# Validate kubectl and vault cli tools
if($null -eq (Get-Command -Name "kubectl.exe")) {
  Write-Host "Kubectl.exe missing from path" -ForegroundColor Yellow;
}

if($null -eq (Get-Command -Name "vault.exe")) {
  Write-Host "vault.exe missing from path" -ForegroundColor Yellow;
}

if($null -eq (Get-Command -Name "kustomize.exe")) {
  Write-Host "kustomize.exe missing from path" -ForegroundColor Yellow;
}

$kubeconfigFile = New-TemporaryFile;
Write-Host "Kubeconfig saved to temp file [$kubeconfigFile]";

$kubeconfig = & vault kv get -field="kubeconfig" -address="$VAULT_ADDR" -namespace="$VAULT_NAMESPACE" $kubeconfigPath

# Older Terraform Module stored yaml as base64, new module stores it as plaintext
if($base64Secret.IsPresent) {
  [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($kubeconfig)) | Set-Content -Path $kubeconfigFile;
} else {
  Set-Content -Path $kubeconfigFile -Value $kubeconfig;
}


Write-Host "`n`nVerifying Cluster Connectivity"
Write-Host "------------------------------"
& kubectl cluster-info --kubeconfig "$kubeconfigFile"

Write-Host "`n`nCreating daemon sets to apply common system configuration"
Write-Host "------------------------------"
kubectl apply --server-side=true -f ./daemonsets --kubeconfig "$kubeconfigFile"

Write-Host "`n`nApplying Cluster Operators/CRDs configuration"
Write-Host "------------------------------"
& kustomize build . | kubectl apply --server-side=true -f - --kubeconfig "$kubeconfigFile"

Write-Host "`n++++++Sleeping for 10s while Operators spin up++++++"
Start-Sleep -Seconds 10;

Write-Host "`n`nApplying Observability Stack configuration"
Write-Host "-----------------------------"
& kubectl create namespace observability --kubeconfig "$kubeconfigFile"
& kustomize build ./observability/logs | kubectl apply --server-side=true -f - --kubeconfig "$kubeconfigFile"
& kustomize build ./observability/metrics | kubectl apply --server-side=true -f - --kubeconfig "$kubeconfigFile"
& kustomize build ./observability/grafana | kubectl apply --server-side=true -f - --kubeconfig "$kubeconfigFile"

Write-Host "`n`nApplying ElasticSearch configuration"
Write-Host "-----------------------------"
kustomize build ./elasticsearch | kubectl apply --server-side=true -f - --kubeconfig "$kubeconfigFile"

Write-Host "`n`nApplying ArgoCD configuration"
Write-Host "-----------------------------"
& kustomize build ./argocd | kubectl apply --server-side=true -f - --kubeconfig "$kubeconfigFile"

Write-Host "`n++++++Sleeping for 10s while ArgoCD spin up++++++"
Start-Sleep -Seconds 10;

Write-Host "`n`nApplying Cluster Argo Project and Applications"
Write-Host "----------------------------------------------"
& kubectl apply --server-side=true -f ./cluster-projects.yaml --kubeconfig "$kubeconfigFile"
& kubectl apply --server-side=true -f ./cluster-applications.yaml --kubeconfig "$kubeconfigFile"

$retry = 1;
$retryMax = 100;
$dns = kubectl get ingress argocd-server-ingress --namespace=argocd -o jsonpath="{.spec.rules[0].host}" --kubeconfig="$kubeconfigFile"

Write-Host "`n`nWaiting for the ArgoCD Ingress' DNS to become available"
Write-Host ".....Waiting for [$dns] to become available"
Write-Host "------------------------------------------------"

do {
  Write-Progress -Activity 'Waiting on ArgoCD Ingress' -PercentComplete $($retry/$retryMax) -Status "Attempt $retry"

  if( (Resolve-DnsName -Name $dns -DnsOnly -ErrorAction SilentlyContinue).Count -eq 1 ) {
    break; #exit the loop
  }

  Start-Sleep -Seconds 10;
  $retry++;
} while ($retry -le $retryMax)

if($retry -ge $retryMax) {
  Write-Host "Cluster has not completed setup within the timelimit. Please manually validate that ArgoCD isn't blocked via logging into ArgoCD with the provided admin credentials and port-forwarding" -ForegroundColor Red;
  Write-Host "`tRun: 'kubectl port-forward svc/argocd-server -n argocd 8080:8080 --kubeconfig=`"$kubeconfigFile`"' and navigating to http://localhost:8080" -ForegroundColor Red;
} else {
  Write-Host "`n`nCluster setup has completed"
  Write-Host "---------------------------"
  Write-Host "ArgoCD is accessible at https://$dns/"

  Write-Host "ArgoCD will have begun to bring up the other cluster application once it started. Please validate their proper startup via the ArgoCD UI"
}

