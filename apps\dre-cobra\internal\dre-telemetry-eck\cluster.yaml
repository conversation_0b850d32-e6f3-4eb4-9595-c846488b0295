apiVersion: elasticsearch.k8s.elastic.co/v1
kind: Elasticsearch
metadata:
  name: dre-telemetry-eck
spec:
  version: 8.11.3
  http:
    tls:
      selfSignedCertificate:
        disabled: true
  volumeClaimDeletePolicy: DeleteOnScaledownAndClusterDeletion
  nodeSets:
    - name: masters
      count: 3
      podTemplate:
        spec:
          containers:
            - name: elasticsearch
              resources:
                requests:
                  cpu: 1
                  memory: 4Gi
                limits:
                  cpu: 2
                  memory: 4Gi
              env:
                # heap size: https://www.elastic.co/guide/en/elasticsearch/reference/current/advanced-configuration.html#set-jvm-heap-size
                - name: ES_JAVA_OPTS
                  value: -Xms2g -Xmx2g
      config:
        node.roles: [master, data]
        bootstrap.memory_lock: false
        reindex.remote.whitelist: "dre-metrics-eck.cobra.dre.ea.com:443, elipy-telemetry.dre.dice.se:443"
      volumeClaimTemplates:
        - metadata:
            name: elasticsearch-data # Do not change this name unless you set up a volume mount for the data path.
          spec:
            accessModes:
              - ReadWriteOnce
            resources:
              requests:
                storage: 100Gi
            storageClassName: default
