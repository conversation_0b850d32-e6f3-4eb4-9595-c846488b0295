apiVersion: apps/v1
kind: Deployment
metadata:
  name: vmdk-deleter
  labels:
    app: vmdk-deleter
spec:
  replicas: 1
  selector:
    matchLabels:
      app: vmdk-deleter
  template:
    metadata:
      labels:
        app: vmdk-deleter
    spec:
      containers:
      - name: vmdk-deleter
        image: dre-docker-federated.artifacts.ea.com/cobra/vmdk-deletion:1.0.117
        imagePullPolicy: "Always"
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        envFrom:
          - secretRef:
              name: vmdk-deleter-secrets
