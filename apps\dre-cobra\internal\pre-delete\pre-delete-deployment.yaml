apiVersion: apps/v1
kind: Deployment
metadata:
  name: pre-delete
  labels:
    app: pre-delete
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pre-delete
  template:
    metadata:
      labels:
        app: pre-delete
    spec:
      containers:
      - name: pre-delete
        image: dre-docker-federated.artifacts.ea.com/cobra/pre-delete/prod/predelete:latest
        imagePullPolicy: "Always"
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        envFrom:
          - secretRef:
              name: pre-delete-secrets
