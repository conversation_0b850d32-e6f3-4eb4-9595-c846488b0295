---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: clustersetup-vaultstore
  namespace: observability
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-setup
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: ldap-secrets
  namespace: observability
spec:
  secretStoreRef:
    name: clustersetup-vaultstore
    kind: SecretStore
  refreshInterval: 720h # 30 days
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
  dataFrom:
    - extract:
        key: cobra/automation/kubernetes/azure/dre-stockholm/argocd/argocd-secret
