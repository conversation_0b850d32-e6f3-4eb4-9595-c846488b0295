apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: alertmanager
  namespace: observability
  annotations:
    # Use SSL
    traefik.ingress.kubernetes.io/router.entrypoints: web,websecure
    traefik.ingress.kubernetes.io/router.middlewares: "traefik-https-redirect@kubernetescrd"
spec:
  rules:
  - host: alertmanager.cobra.dre.ea.com
    http:
      paths:
      - backend:
          service:
            name: alertmanager-main
            port:
              name: web
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - alertmanager.cobra.dre.ea.com