apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: bct-bilbo-eck-ingress
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.middlewares: traefik-https-redirect@kubernetescrd
spec:
  tls:
    - hosts:
        - bct-bilbo-eck.cobra.dre.ea.com
  rules:
    - host: bct-bilbo-eck.cobra.dre.ea.com
      http:
        paths:
          - backend:
              service:
                name: bct-bilbo-eck-es-http
                port:
                  number: 9200
            path: /
            pathType: Prefix
