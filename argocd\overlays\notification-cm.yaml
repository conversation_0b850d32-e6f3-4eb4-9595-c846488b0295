apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-notifications-cm
data:
  context: |
    argocdUrl: "https://argocd-dice.cobra.dre.ea.com/"
  service.slack: |
    token: $slack-token
  subscriptions: |
    # subscription for on-sync-status-unknown trigger notifications
    - recipients:
      - slack:cobra-infra
      triggers:
      # - on-sync-status-unknown
      - on-deployed
      - on-health-degraded
      - on-sync-failed
  defaultTriggers: |
    # Holds list of triggers that are used by default if trigger is not specified explicitly in the subscription
    # - on-sync-status-unknown
    - on-deployed
    - on-health-degraded
    - on-sync-failed
