apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: argocd
  name: argocd-server-ingress
  annotations:
    # Use SSL
    traefik.ingress.kubernetes.io/router.entrypoints: "websecure"
    traefik.ingress.kubernetes.io/router.middlewares: "traefik-https-redirect@kubernetescrd"
spec:
  tls:
    - hosts:
        - argocd-dice.cobra.dre.ea.com
  rules:
    - host: argocd-dice.cobra.dre.ea.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: argocd-server
                port:
                  name: http
