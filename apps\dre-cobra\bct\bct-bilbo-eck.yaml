apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: bct-bilbo-eck
  namespace: argocd
  labels:
    project: bct
    group: elasticsearch
spec:
  project: bct
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    path: ./apps/dre-cobra/bct/bct-bilbo-eck
    targetRevision: master
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: bct
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
    # automated:
    #   prune: false
    #   selfHeal: false
