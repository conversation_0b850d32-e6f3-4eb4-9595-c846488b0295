kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
  name: traefik-admin
  namespace: traefik
  annotations:
    # Use SSL
    traefik.ingress.kubernetes.io/router.entrypoints: "websecure"
    traefik.ingress.kubernetes.io/router.middlewares: "traefik-https-redirect@kubernetescrd"
spec:
  tls:
    - hosts:
        - traefik.cobra.dre.ea.com
  rules:
    - host: traefik.cobra.dre.ea.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: traefik-admin
                port:
                  number: 9000