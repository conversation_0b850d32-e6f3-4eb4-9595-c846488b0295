apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: cobra-loki
  namespace: argocd
  labels:
    project: observability
    group: loki
spec:
  project: observability
  source:
    repoURL: https://artifactory.eu.ea.com/artifactory/dreeu-helm-virtual
    targetRevision: 5.48.0
    chart: loki
    helm:
      values: |
        loki:
          auth_enabled: false
          commonConfig:
            path_prefix: /var/loki
          schemaConfig:
            configs:
              - from: "2023-12-01"
                index:
                  period: 24h
                  prefix: loki_index_
                object_store: azure
                schema: v12
                store: tsdb
          storage:
            azure:
              accountName: ${AZ_STORAGE_ACCOUNT_NAME}
              accountKey: ${AZ_STORAGE_ACCOUNT_KEY}
              requestTimeout: 2m
            bucketNames:
              chunks: ${AZ_LOGS_STORAGE_CONTAINER_NAME}
              ruler: ${AZ_LOGS_STORAGE_CONTAINER_NAME}
            filesystem:
              chunks_directory: /var/loki/chunks
              rules_directory: /var/loki/rules
            type: azure
          storage_config:
            tsdb_shipper:
              active_index_directory: /var/loki/tsdb-index
              cache_location: /var/loki/tsdb-cache
              cache_ttl: 24h
              shared_store: azure
            hedging:
              at: "250ms"
              max_per_second: 20
              up_to: 3
          query_scheduler:
            max_outstanding_requests_per_tenant: 4096
          frontend:
            max_outstanding_per_tenant: 4096
          limits_config:
            ingestion_rate_mb: 6
            ingestion_burst_size_mb: 10
            retention_period: 4320h
            query_timeout: 2m
          compactor:
            working_directory: /var/loki/retention
            shared_store: azure
            compaction_interval: 10m
            retention_enabled: true
            retention_delete_delay: 2h
            retention_delete_worker_count: 150
          server:
            http_server_read_timeout: 2m
            http_server_write_timeout: 30s
            http_server_idle_timeout: 2m
        monitoring:
          dashboards:
            namespace: observability
          selfMonitoring:
            enabled: false
            grafanaAgent:
              installOperator: false
        test:
          enabled: false # https://github.com/grafana/loki/issues/9663
        backend:
          extraEnvFrom:
            - secretRef:
                name: cobra-loki-secrets
          extraArgs:
            - "-config.expand-env=true"
        read:
          extraEnvFrom:
            - secretRef:
                name: cobra-loki-secrets
          extraArgs:
            - "-config.expand-env=true"
        write:
          extraEnvFrom:
            - secretRef:
                name: cobra-loki-secrets
          extraArgs:
            - "-config.expand-env=true"
        gateway:
          image:
            registry: docker.artifacts.ea.com
            repository: nginxinc/nginx-unprivileged
            tag: 1.27-alpine
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: application-observability
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
    automated:
      prune: true
      selfHeal: true