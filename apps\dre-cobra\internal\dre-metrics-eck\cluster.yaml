apiVersion: elasticsearch.k8s.elastic.co/v1
kind: Elasticsearch
metadata:
  name: dre-metrics-eck
spec:
  version: 6.8.23
  http:
    tls:
      selfSignedCertificate:
        disabled: true
  volumeClaimDeletePolicy: DeleteOnScaledownAndClusterDeletion
  nodeSets:
    - name: masters
      count: 3
      podTemplate:
        spec:
          containers:
            - name: elasticsearch
              resources:
                requests:
                  cpu: 4
                  memory: 6Gi
                limits:
                  memory: 6Gi
              env:
                # heap size: https://www.elastic.co/guide/en/elasticsearch/reference/current/advanced-configuration.html#set-jvm-heap-size
                - name: ES_JAVA_OPTS
                  value: -Xms3g -Xmx3g
      config:
        node.master: true
        node.data: false
        node.ingest: false
        bootstrap.memory_lock: false
        reindex.remote.whitelist: "dst-metrics-1.dre.dice.se:443, dre-es-metrics-test.dre.dice.se:443, elipy-telemetry.dre.dice.se:443"
        xpack.security.authc:
          anonymous:
            username: anonymous
            roles: superuser
            authz_exception: false
      volumeClaimTemplates:
        - metadata:
            name: elasticsearch-data # Do not change this name unless you set up a volume mount for the data path.
          spec:
            accessModes:
              - ReadWriteOnce
            resources:
              requests:
                storage: 256Gi
            storageClassName: default
    - name: data-nodes
      count: 4
      podTemplate:
        spec:
          containers:
            - name: elasticsearch
              resources:
                requests:
                  cpu: 5
                  memory: 10Gi
                limits:
                  memory: 10Gi
              env:
                # heap size: https://www.elastic.co/guide/en/elasticsearch/reference/current/advanced-configuration.html#set-jvm-heap-size
                - name: ES_JAVA_OPTS
                  value: -Xms5g -Xmx5g
      config:
        node.master: false
        node.data: true
        node.ingest: false
        bootstrap.memory_lock: false
        reindex.remote.whitelist: kin-metrics-es.dre.dice.se:443, fb1-metrics-1.dre.dice.se:443, gnt-metrics-es.dre.dice.se:443
        xpack.security.authc:
          anonymous:
            username: anonymous
            roles: superuser
            authz_exception: false
      volumeClaimTemplates:
        - metadata:
            name: elasticsearch-data
          spec:
            accessModes:
              - ReadWriteOnce
            resources:
              requests:
                storage: 300Gi
            storageClassName: default
