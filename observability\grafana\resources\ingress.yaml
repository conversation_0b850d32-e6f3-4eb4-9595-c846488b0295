apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: observability
  name: grafana
  annotations:
    # Use SSL
    traefik.ingress.kubernetes.io/router.entrypoints: web,websecure
    traefik.ingress.kubernetes.io/router.middlewares: "traefik-https-redirect@kubernetescrd"
spec:
  tls:
    - hosts:
        - grafana-dice.cobra.dre.ea.com
  rules:
    - host: grafana-dice.cobra.dre.ea.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: grafana-service
                port:
                  name: grafana
