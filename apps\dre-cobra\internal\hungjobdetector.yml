apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: hungjobdetector
  namespace: argocd
  labels:
    project: internal
    group: hungjobdetector
spec:
  project: internal
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    path: ./apps/dre-cobra/internal/hungjobdetector
    targetRevision: master
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: hungjobdetector
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
  #   automated:
  #     prune: true
  #     selfHeal: true
