---
apiVersion: elasticsearch.k8s.elastic.co/v1
kind: Elasticsearch
metadata:
  name: test-bilbo-eck
spec:
  version: 6.8.23
  http:
    tls:
      selfSignedCertificate:
        disabled: true
  volumeClaimDeletePolicy: DeleteOnScaledownOnly
  nodeSets:
    - name: masters
      count: 1
      podTemplate:
        spec:
          containers:
            - name: elasticsearch
              resources:
                requests:
                  cpu: 1000m
                  memory: 4Gi
                limits:
                  cpu: 1000m
                  memory: 4Gi
      config:
        node.master: true
        node.data: true
        node.ingest: false
        bootstrap.memory_lock: false
        reindex.remote.whitelist: bct-bilbo-eck.cobra.dre.ea.com:443
        xpack.security.authc:
          anonymous:
            username: anonymous
            roles: superuser
            authz_exception: false
      volumeClaimTemplates:
        - metadata:
            name: elasticsearch-data # Do not change this name unless you set up a volume mount for the data path.
          spec:
            accessModes:
              - ReadWriteOnce
            resources:
              requests:
                storage: 10Gi
            storageClassName: default
