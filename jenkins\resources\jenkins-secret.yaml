---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: clustersetup-vaultstore
  namespace: cobra
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-setup  
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  labels:
    app.kubernetes.io/name: jenkins-secret
  name: jenkins-secret
  namespace: cobra
spec:
  secretStoreRef:
    name: clustersetup-vaultstore
    kind: SecretStore
  refreshInterval: "0"
  target:
    template:
      type: kubernetes.io/dockerconfigjson
      data:
        .dockerconfigjson: "{{ .docker_auth | toString }}"
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
  data:
    - secretKey: docker_auth
      remoteRef:
        key: cobra/automation/kubernetes/azure/dre-stockholm/argocd/artifactory_registry
        property: docker_artifactory_auth