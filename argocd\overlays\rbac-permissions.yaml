apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-rbac-cm
data:
  policy.csv: |
    # Cluster Admins
    p, role:service-restart, applications, action/restart, */*, allow
    p, role:service-restart, applications, get, */*, allow
    p, role:service-restart, applications, list, */*, allow
    p, role:service-restart, projects, get, */*, allow
    p, role:service-restart, projects, list, */*, allow
    g, DRE.JENKINS.SANTIAGO.ADMINS, role:service-restart
    g, DRE.JENKINS.SANTIAGO.ADMINS, role:read-only
    g, DRE.Cobra.Engineers, role:admin
    g, dre-k8s-platformteam, role:admin
    # Backstage service account
    p, role:backstage, applications, get, */*, allow
    p, role:backstage, applications, list, */*, allow
    g, backstage, role:backstage
