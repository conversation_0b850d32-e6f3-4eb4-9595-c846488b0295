---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: vc-monitoring-vaultstore
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-apps-internal

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  labels:
    app.kubernetes.io/name: vc-monitoring-secrets
    app.kubernetes.io/part-of: vc-monitoring
  name: vc-monitoring-secrets
spec:
  secretStoreRef:
    name: vc-monitoring-vaultstore
    kind: SecretStore
  refreshInterval: "12h"
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
        labels:
          argocd.argoproj.io/secret-type: repo-creds
  data:
    - secretKey: VSPHERE_USER
      remoteRef:
        key: cobra/automation/vsphere/global
        property: USERNAME
    - secretKey: VSPHERE_DOMAIN
      remoteRef:
        key: cobra/automation/vsphere/global
        property: DOMAIN
    - secretKey: VSPHERE_PASSWORD
      remoteRef:
        key: cobra/automation/vsphere/global
        property: PASSWORD
  dataFrom:
    - extract:
        key: cobra/automation/slack/cobra-bot-sthlm-luns
