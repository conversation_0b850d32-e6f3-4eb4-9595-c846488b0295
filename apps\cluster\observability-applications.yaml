apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: cs-observability-metrics
  # You'll usually want to add your resources to the argocd namespace.
  namespace: argocd
  # Add a this finalizer ONLY if you want these to cascade delete.
  # finalizers:
  #   - resources-finalizer.argocd.argoproj.io
spec:
  generators:
  - list:
      elements:
      - app: blackbox
      - app: kube-state-metrics
      - app: node-exporter
      - app: prometheus
      - app: alertmanager
  template:
    metadata:
      name: 'cs-observability-{{app}}'
    spec:
      # The project the application belongs to.
      project: cluster-setup
      source:
        repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
        targetRevision: master
        path: ./observability/metrics/{{app}}
      destination:
        server: https://kubernetes.default.svc
        namespace: observability
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: cs-observability-logs
  # You'll usually want to add your resources to the argocd namespace.
  namespace: argocd
  # Add a this finalizer ONLY if you want these to cascade delete.
  # finalizers:
  #   - resources-finalizer.argocd.argoproj.io
spec:
  generators:
  - list:
      elements:
      - app: vector
      - app: loki
  template:
    metadata:
      name: 'cs-observability-{{app}}'
    spec:
      # The project the application belongs to.
      project: cluster-setup
      source:
        repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
        targetRevision: master
        path: ./observability/logs/{{app}}
      destination:
        server: https://kubernetes.default.svc
        namespace: observability
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: cs-observability-grafana
  # You'll usually want to add your resources to the argocd namespace.
  namespace: argocd
  # Add a this finalizer ONLY if you want these to cascade delete.
  # finalizers:
  #   - resources-finalizer.argocd.argoproj.io
spec:
  # The project the application belongs to.
  project: cluster-setup

  # Source of the application manifests
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    targetRevision: master
    path: ./observability/grafana

  # Destination cluster and namespace to deploy the application
  destination:
    server: https://kubernetes.default.svc
    namespace: observability

  syncPolicy:
    automated:
      prune: true
      selfHeal: true