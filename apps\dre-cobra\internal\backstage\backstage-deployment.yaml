---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backstage
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backstage
  template:
    metadata:
      labels:
        app: backstage
    spec:
      containers:
        - name: backstage
          image: dre-docker-federated.artifacts.ea.com/cobra/backstage:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 7007
          resources:
            requests:
              cpu: "100m"
              memory: "10Gi"
            limits:
              cpu: "2"
              memory: "10Gi"
          env:
            - name: POSTGRES_HOST
              value: backstage-postgres-svc.internal
            - name: POSTGRES_PORT
              value: '5432'
          envFrom:
            - secretRef:
                name: backstage-secrets
