apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: quickscope-mongodb
  namespace: argocd
  labels:
    project: misc
    group: mongodb
spec:
  project: misc
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/helm-charts/mongodb.git
    path: "."
    targetRevision: 0.0.8
    helm:
      values: |
        instanceName: quickscope
        fqdn: quickscope-mongodb.cobra.dre.ea.com
        version: 3.2.12
        storage: 1000Gi
        resources:
          requests:
            cpu: "1"
            memory: 16Gi
          limits:
            cpu: "4"
            memory: 16Gi
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: misc
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
  #   automated:
  #     prune: true
  #     selfHeal: true
