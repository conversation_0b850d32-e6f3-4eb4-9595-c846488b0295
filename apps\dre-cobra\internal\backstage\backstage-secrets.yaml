---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: backstage-vaultstore
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-apps-internal

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: backstage-secrets
spec:
  secretStoreRef:
    name: backstage-vaultstore
    kind: SecretStore
  refreshInterval: 1h
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
  dataFrom:
    - extract:
        key: cobra/automation/services/backstage
