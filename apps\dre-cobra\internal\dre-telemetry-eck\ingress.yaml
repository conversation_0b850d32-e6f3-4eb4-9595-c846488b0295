# internal cluster url: http://dre-telemetry-eck-es-http.internal.svc.cluster.local:9200
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dre-telemetry-eck-ingress
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.middlewares: traefik-https-redirect@kubernetescrd
spec:
  tls:
    - hosts:
        - dre-telemetry-eck.cobra.dre.ea.com
  rules:
    - host: dre-telemetry-eck.cobra.dre.ea.com
      http:
        paths:
          - backend:
              service:
                name: dre-telemetry-eck-es-http
                port:
                  number: 9200
            path: /
            pathType: Prefix
