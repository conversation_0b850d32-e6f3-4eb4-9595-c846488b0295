apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: influxdb
spec:
  selector:
    matchLabels:
      instance: influxdb
  serviceName: influxdb
  replicas: 1
  template:
    metadata:
      labels:
        instance: influxdb
    spec:
      containers:
        - name: influxdb
          image: docker.artifacts.ea.com/influxdb:1.7.9
          imagePullPolicy: "Always"
          ports:
            - containerPort: 8086
              name: http
          readinessProbe:
            tcpSocket:
              port: http
            periodSeconds: 10
            timeoutSeconds: 5
          resources:
            requests:
              cpu: "1"
              memory: 16G
            limits:
              cpu: "4"
              memory: 16G
          volumeMounts:
            - mountPath: /var/lib/influxdb
              name: influxdb-pvc
            - mountPath: /etc/influxdb/influxdb.conf
              name: influxdb-cm
              subPath: influxdb.conf
      volumes:
        - name: influxdb-pvc
          persistentVolumeClaim:
            claimName: influxdb-pvc
        - name: influxdb-cm
          configMap:
            name: influxdb-cm
