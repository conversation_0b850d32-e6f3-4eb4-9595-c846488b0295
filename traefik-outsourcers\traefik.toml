[global]
  sendAnonymousUsage = false
  checkNewVersion = false

## Static configuration
[serversTransport]
  insecureSkipVerify = true

[entryPoints]
  [entryPoints.web]
    address = ":8000"

  [entryPoints.websecure]
    address = ":8443"
    [entryPoints.websecure.http]
      [entryPoints.websecure.http.tls]

  [entryPoints.traefik]
    address = ":9000"

  [entrypoints.metrics]
    address = ":9100"

[providers]
  providersThrottleDuration = "30s"

  [providers.kubernetesCRD]
    ingressClass = "traefik-outsourcers"

  [providers.kubernetesIngress]
    ingressClass = "traefik-outsourcers"

  [providers.kubernetesIngress.ingressEndpoint]
    publishedService = "traefik-outsourcers/traefik-outsourcers"

[ping]

[api]
  insecure = true
  dashboard = true

[log]
  level = "INFO"

[metrics]
  [metrics.prometheus]
    entrypoint = "metrics"

[accessLog]
  filePath = "/var/log/traefik/access.log"
  format = "json"
