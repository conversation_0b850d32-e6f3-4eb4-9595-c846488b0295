apiVersion: apps/v1
kind: Deployment
metadata:
  name: dashing
  labels:
    app: dashing
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dashing
  template:
    metadata:
      labels:
        app: dashing
    spec:
      containers:
      - name: dashing
        image: dre-docker-federated.artifacts.ea.com/cobra/dashing:latest
        imagePullPolicy: "Always"
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        ports:
        - containerPort: 3030
          name: web
        envFrom:
          - secretRef:
              name: dashing-secrets