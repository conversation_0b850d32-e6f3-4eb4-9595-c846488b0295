---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: pre-delete-vaultstore
spec:
  provider:
    vault:
      server: "https://ess.ea.com"
      path: "secrets/kv"
      namespace: "cds-dre-prod"
      auth:
        kubernetes:
          mountPath: "kubernetes/cobra/azure/dre-stockholm"
          role: cluster-apps-internal

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  labels:
    app.kubernetes.io/name: pre-delete-secrets
    app.kubernetes.io/part-of: pre-delete
  name: pre-delete-secrets
spec:
  secretStoreRef:
    name: pre-delete-vaultstore
    kind: SecretStore
  refreshInterval: "12h"
  target:
    template:
      metadata:
        annotations:
          argocd.argoproj.io/compare-options: IgnoreExtraneous
        labels:
          argocd.argoproj.io/secret-type: repo-creds
  data:
    - secretKey: JENKINS_USERNAME
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: username
    - secretKey: JENKINS_API_KEY
      remoteRef:
        key: cobra/automation/accounts/monkey.commons
        property: password
  dataFrom:
    - extract:
        key: cobra/automation/services/pre-delete
    - extract:
        key: cobra/automation/slack/dre_cobra_slack_bot