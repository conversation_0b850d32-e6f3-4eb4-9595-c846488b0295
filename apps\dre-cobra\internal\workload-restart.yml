apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: workload-restart
  namespace: argocd
  labels:
    project: internal
    group: utility
spec:
  project: internal
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    path: ./apps/dre-cobra/internal/workload-restart
    targetRevision: master
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: internal
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
    automated:
      prune: true
      selfHeal: true
