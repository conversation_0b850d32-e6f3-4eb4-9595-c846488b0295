# Source: eck-operator/templates/operator-namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: elastic-system
  labels:
    name: elastic-system
---
# Source: eck-operator/templates/service-account.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: elastic-operator
  namespace: elastic-system
  labels:
    control-plane: elastic-operator
    app.kubernetes.io/version: "2.10.0"
---
# Source: eck-operator/templates/webhook.yaml
apiVersion: v1
kind: Secret
metadata:
  name: elastic-webhook-server-cert
  namespace: elastic-system
  labels:
    control-plane: elastic-operator
    app.kubernetes.io/version: "2.10.0"
---
# Source: eck-operator/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: elastic-operator
  namespace: elastic-system
  labels:
    control-plane: elastic-operator
    app.kubernetes.io/version: "2.10.0"
data:
  eck.yaml: |-
    log-verbosity: 0
    metrics-port: 0
    container-registry: docker.elastic.co
    max-concurrent-reconciles: 3
    ca-cert-validity: 8760h
    ca-cert-rotate-before: 24h
    cert-validity: 8760h
    cert-rotate-before: 24h
    exposed-node-labels: [topology.kubernetes.io/.*,failure-domain.beta.kubernetes.io/.*]
    set-default-security-context: auto-detect
    kube-client-timeout: 60s
    elasticsearch-client-timeout: 180s
    disable-telemetry: false
    distribution-channel: all-in-one
    validate-storage-class: true
    enable-webhook: true
    webhook-name: elastic-webhook.k8s.elastic.co
    enable-leader-election: true
---
# Source: eck-operator/templates/cluster-roles.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: elastic-operator
  labels:
    control-plane: elastic-operator
    app.kubernetes.io/version: "2.10.0"
rules:
- apiGroups:
  - "authorization.k8s.io"
  resources:
  - subjectaccessreviews
  verbs:
  - create
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - create
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  resourceNames:
  - elastic-operator-leader
  verbs:
  - get
  - watch
  - update
- apiGroups:
  - ""
  resources:
  - endpoints
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  - events
  - persistentvolumeclaims
  - secrets
  - services
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - apps
  resources:
  - deployments
  - statefulsets
  - daemonsets
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - policy
  resources:
  - poddisruptionbudgets
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - elasticsearch.k8s.elastic.co
  resources:
  - elasticsearches
  - elasticsearches/status
  - elasticsearches/finalizers # needed for ownerReferences with blockOwnerDeletion on OCP
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
- apiGroups:
  - kibana.k8s.elastic.co
  resources:
  - kibanas
  - kibanas/status
  - kibanas/finalizers # needed for ownerReferences with blockOwnerDeletion on OCP
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
- apiGroups:
  - apm.k8s.elastic.co
  resources:
  - apmservers
  - apmservers/status
  - apmservers/finalizers # needed for ownerReferences with blockOwnerDeletion on OCP
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
- apiGroups:
  - enterprisesearch.k8s.elastic.co
  resources:
  - enterprisesearches
  - enterprisesearches/status
  - enterprisesearches/finalizers # needed for ownerReferences with blockOwnerDeletion on OCP
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
- apiGroups:
  - beat.k8s.elastic.co
  resources:
  - beats
  - beats/status
  - beats/finalizers # needed for ownerReferences with blockOwnerDeletion on OCP
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
- apiGroups:
  - agent.k8s.elastic.co
  resources:
  - agents
  - agents/status
  - agents/finalizers # needed for ownerReferences with blockOwnerDeletion on OCP
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
- apiGroups:
  - maps.k8s.elastic.co
  resources:
  - elasticmapsservers
  - elasticmapsservers/status
  - elasticmapsservers/finalizers # needed for ownerReferences with blockOwnerDeletion on OCP
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
- apiGroups:
  - storage.k8s.io
  resources:
  - storageclasses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - admissionregistration.k8s.io
  resources:
  - validatingwebhookconfigurations
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - nodes
  verbs:
  - get
  - list
  - watch
---
# Source: eck-operator/templates/cluster-roles.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: "elastic-operator-view"
  labels:
    rbac.authorization.k8s.io/aggregate-to-view: "true"
    rbac.authorization.k8s.io/aggregate-to-edit: "true"
    rbac.authorization.k8s.io/aggregate-to-admin: "true"
    control-plane: elastic-operator
    app.kubernetes.io/version: "2.10.0"
rules:
- apiGroups: ["elasticsearch.k8s.elastic.co"]
  resources: ["elasticsearches"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apm.k8s.elastic.co"]
  resources: ["apmservers"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["kibana.k8s.elastic.co"]
  resources: ["kibanas"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["enterprisesearch.k8s.elastic.co"]
  resources: ["enterprisesearches"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["beat.k8s.elastic.co"]
  resources: ["beats"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["agent.k8s.elastic.co"]
  resources: ["agents"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["maps.k8s.elastic.co"]
  resources: ["elasticmapsservers"]
  verbs: ["get", "list", "watch"]
---
# Source: eck-operator/templates/cluster-roles.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: "elastic-operator-edit"
  labels:
    rbac.authorization.k8s.io/aggregate-to-edit: "true"
    rbac.authorization.k8s.io/aggregate-to-admin: "true"
    control-plane: elastic-operator
    app.kubernetes.io/version: "2.10.0"
rules:
- apiGroups: ["elasticsearch.k8s.elastic.co"]
  resources: ["elasticsearches"]
  verbs: ["create", "delete", "deletecollection", "patch", "update"]
- apiGroups: ["apm.k8s.elastic.co"]
  resources: ["apmservers"]
  verbs: ["create", "delete", "deletecollection", "patch", "update"]
- apiGroups: ["kibana.k8s.elastic.co"]
  resources: ["kibanas"]
  verbs: ["create", "delete", "deletecollection", "patch", "update"]
- apiGroups: ["enterprisesearch.k8s.elastic.co"]
  resources: ["enterprisesearches"]
  verbs: ["create", "delete", "deletecollection", "patch", "update"]
- apiGroups: ["beat.k8s.elastic.co"]
  resources: ["beats"]
  verbs: ["create", "delete", "deletecollection", "patch", "update"]
- apiGroups: ["agent.k8s.elastic.co"]
  resources: ["agents"]
  verbs: ["create", "delete", "deletecollection", "patch", "update"]
- apiGroups: ["maps.k8s.elastic.co"]
  resources: ["elasticmapsservers"]
  verbs: ["create", "delete", "deletecollection", "patch", "update"]
---
# Source: eck-operator/templates/role-bindings.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: elastic-operator
  labels:
    control-plane: elastic-operator
    app.kubernetes.io/version: "2.10.0"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: elastic-operator
subjects:
- kind: ServiceAccount
  name: elastic-operator
  namespace: elastic-system
---
# Source: eck-operator/templates/webhook.yaml
apiVersion: v1
kind: Service
metadata:
  name: elastic-webhook-server
  namespace: elastic-system
  labels:
    control-plane: elastic-operator
    app.kubernetes.io/version: "2.10.0"
spec:
  ports:
  - name: https
    port: 443
    targetPort: 9443
  selector:
    control-plane: elastic-operator
---
# Source: eck-operator/templates/statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: elastic-operator
  namespace: elastic-system
  labels:
    control-plane: elastic-operator
    app.kubernetes.io/version: "2.10.0"
spec:
  selector:
    matchLabels:
      control-plane: elastic-operator
  serviceName: elastic-operator
  replicas: 1
  template:
    metadata:
      annotations:
        # Rename the fields "error" to "error.message" and "source" to "event.source"
        # This is to avoid a conflict with the ECS "error" and "source" documents.
        "co.elastic.logs/raw": "[{\"type\":\"container\",\"json.keys_under_root\":true,\"paths\":[\"/var/log/containers/*${data.kubernetes.container.id}.log\"],\"processors\":[{\"convert\":{\"mode\":\"rename\",\"ignore_missing\":true,\"fields\":[{\"from\":\"error\",\"to\":\"_error\"}]}},{\"convert\":{\"mode\":\"rename\",\"ignore_missing\":true,\"fields\":[{\"from\":\"_error\",\"to\":\"error.message\"}]}},{\"convert\":{\"mode\":\"rename\",\"ignore_missing\":true,\"fields\":[{\"from\":\"source\",\"to\":\"_source\"}]}},{\"convert\":{\"mode\":\"rename\",\"ignore_missing\":true,\"fields\":[{\"from\":\"_source\",\"to\":\"event.source\"}]}}]}]"
        "checksum/config": a99a5f63f628a1ca8df440c12506cdfbf17827a1175dc5765b05f22f92b12b95
      labels:
        control-plane: elastic-operator
    spec:
      terminationGracePeriodSeconds: 10
      serviceAccountName: elastic-operator
      securityContext:
        runAsNonRoot: true
      containers:
      - image: "docker.elastic.co/eck/eck-operator:2.10.0"
        imagePullPolicy: IfNotPresent
        name: manager
        args:
        - "manager"
        - "--config=/conf/eck.yaml"
        env:
        - name: OPERATOR_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: WEBHOOK_SECRET
          value: elastic-webhook-server-cert
        resources:
          limits:
            cpu: 1
            memory: 1Gi
          requests:
            cpu: 100m
            memory: 150Mi
        ports:
        - containerPort: 9443
          name: https-webhook
          protocol: TCP
        volumeMounts:
        - mountPath: "/conf"
          name: conf
          readOnly: true
        - mountPath: /tmp/k8s-webhook-server/serving-certs
          name: cert
          readOnly: true
      volumes:
      - name: conf
        configMap:
          name: elastic-operator
      - name: cert
        secret:
          defaultMode: 420
          secretName: elastic-webhook-server-cert
---
# Source: eck-operator/templates/webhook.yaml
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: elastic-webhook.k8s.elastic.co
  labels:
    control-plane: elastic-operator
    app.kubernetes.io/version: "2.10.0"
webhooks:
- clientConfig:
    caBundle: Cg==
    service:
      name: elastic-webhook-server
      namespace: elastic-system
      path: /validate-agent-k8s-elastic-co-v1alpha1-agent
  failurePolicy: Ignore
  name: elastic-agent-validation-v1alpha1.k8s.elastic.co
  matchPolicy: Exact
  admissionReviewVersions: [v1beta1]
  sideEffects: None
  rules:
  - apiGroups:
    - agent.k8s.elastic.co
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - agents
- clientConfig:
    caBundle: Cg==
    service:
      name: elastic-webhook-server
      namespace: elastic-system
      path: /validate-apm-k8s-elastic-co-v1-apmserver
  failurePolicy: Ignore
  name: elastic-apm-validation-v1.k8s.elastic.co
  matchPolicy: Exact
  admissionReviewVersions: [v1beta1]
  sideEffects: None
  rules:
  - apiGroups:
    - apm.k8s.elastic.co
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    resources:
    - apmservers
- clientConfig:
    caBundle: Cg==
    service:
      name: elastic-webhook-server
      namespace: elastic-system
      path: /validate-apm-k8s-elastic-co-v1beta1-apmserver
  failurePolicy: Ignore
  name: elastic-apm-validation-v1beta1.k8s.elastic.co
  matchPolicy: Exact
  admissionReviewVersions: [v1beta1]
  sideEffects: None
  rules:
  - apiGroups:
    - apm.k8s.elastic.co
    apiVersions:
    - v1beta1
    operations:
    - CREATE
    - UPDATE
    resources:
    - apmservers
- clientConfig:
    caBundle: Cg==
    service:
      name: elastic-webhook-server
      namespace: elastic-system
      path: /validate-beat-k8s-elastic-co-v1beta1-beat
  failurePolicy: Ignore
  name: elastic-beat-validation-v1beta1.k8s.elastic.co
  matchPolicy: Exact
  admissionReviewVersions: [v1beta1]
  sideEffects: None
  rules:
  - apiGroups:
    - beat.k8s.elastic.co
    apiVersions:
    - v1beta1
    operations:
    - CREATE
    - UPDATE
    resources:
    - beats
- clientConfig:
    caBundle: Cg==
    service:
      name: elastic-webhook-server
      namespace: elastic-system
      path: /validate-enterprisesearch-k8s-elastic-co-v1-enterprisesearch
  failurePolicy: Ignore
  name: elastic-ent-validation-v1.k8s.elastic.co
  matchPolicy: Exact
  admissionReviewVersions: [v1beta1]
  sideEffects: None
  rules:
  - apiGroups:
    - enterprisesearch.k8s.elastic.co
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    resources:
    - enterprisesearches
- clientConfig:
    caBundle: Cg==
    service:
      name: elastic-webhook-server
      namespace: elastic-system
      path: /validate-enterprisesearch-k8s-elastic-co-v1beta1-enterprisesearch
  failurePolicy: Ignore
  name: elastic-ent-validation-v1beta1.k8s.elastic.co
  matchPolicy: Exact
  admissionReviewVersions: [v1beta1]
  sideEffects: None
  rules:
  - apiGroups:
    - enterprisesearch.k8s.elastic.co
    apiVersions:
    - v1beta1
    operations:
    - CREATE
    - UPDATE
    resources:
    - enterprisesearches
- clientConfig:
    caBundle: Cg==
    service:
      name: elastic-webhook-server
      namespace: elastic-system
      path: /validate-elasticsearch-k8s-elastic-co-v1-elasticsearch
  failurePolicy: Ignore
  name: elastic-es-validation-v1.k8s.elastic.co
  matchPolicy: Exact
  admissionReviewVersions: [v1beta1]
  sideEffects: None
  rules:
  - apiGroups:
    - elasticsearch.k8s.elastic.co
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    resources:
    - elasticsearches
- clientConfig:
    caBundle: Cg==
    service:
      name: elastic-webhook-server
      namespace: elastic-system
      path: /validate-elasticsearch-k8s-elastic-co-v1beta1-elasticsearch
  failurePolicy: Ignore
  name: elastic-es-validation-v1beta1.k8s.elastic.co
  matchPolicy: Exact
  admissionReviewVersions: [v1beta1]
  sideEffects: None
  rules:
  - apiGroups:
    - elasticsearch.k8s.elastic.co
    apiVersions:
    - v1beta1
    operations:
    - CREATE
    - UPDATE
    resources:
    - elasticsearches
- clientConfig:
    caBundle: Cg==
    service:
      name: elastic-webhook-server
      namespace: elastic-system
      path: /validate-kibana-k8s-elastic-co-v1-kibana
  failurePolicy: Ignore
  name: elastic-kb-validation-v1.k8s.elastic.co
  matchPolicy: Exact
  admissionReviewVersions: [v1beta1]
  sideEffects: None
  rules:
  - apiGroups:
    - kibana.k8s.elastic.co
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    resources:
    - kibanas
- clientConfig:
    caBundle: Cg==
    service:
      name: elastic-webhook-server
      namespace: elastic-system
      path: /validate-kibana-k8s-elastic-co-v1beta1-kibana
  failurePolicy: Ignore
  name: elastic-kb-validation-v1beta1.k8s.elastic.co
  matchPolicy: Exact
  admissionReviewVersions: [v1beta1]
  sideEffects: None
  rules:
  - apiGroups:
    - kibana.k8s.elastic.co
    apiVersions:
    - v1beta1
    operations:
    - CREATE
    - UPDATE
    resources:
    - kibanas

