apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: gnt-bilbo-eck
  namespace: argocd
  labels:
    project: granite
    group: elasticsearch
spec:
  project: granite
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    path: ./apps/dre-cobra/granite/gnt-bilbo-eck
    targetRevision: master
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: granite
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
    # automated:
    #   prune: false
    #   selfHeal: false
