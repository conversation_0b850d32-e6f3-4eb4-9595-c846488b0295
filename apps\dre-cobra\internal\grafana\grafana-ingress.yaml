apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: grafana-ingress
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: web,websecure
    traefik.ingress.kubernetes.io/router.middlewares: traefik-https-redirect@kubernetescrd
spec:
  tls:
    - hosts:
        - grafana.cobra.dre.ea.com
  rules:
    - host: grafana.cobra.dre.ea.com
      http:
        paths:
          - backend:
              service:
                name: cobra-grafana-service
                port:
                  name: http-grafana
            path: /
            pathType: Prefix
