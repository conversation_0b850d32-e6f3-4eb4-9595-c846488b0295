apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: kin-bilbo-eck
  namespace: argocd
  labels:
    project: kingston
    group: elasticsearch
spec:
  project: kingston
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/cluster.git
    path: ./apps/dre-cobra/kingston/dice-kin-bilbo-eck
    targetRevision: master
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: kingston
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
    # automated:
    #   prune: false
    #   selfHeal: false