apiVersion: apps/v1
kind: Deployment
metadata:
  name: telegraf-vsphere
spec:
  replicas: 1
  selector:
    matchLabels:
      app: telegraf-vsphere
  template:
    metadata:
      labels:
        app: telegraf-vsphere
    spec:
      containers:
        - name: telegraf-vsphere
          image: docker.artifacts.ea.com/telegraf:1.12.4-alpine
          envFrom:
            - secretRef:
                name: telegraf-vsphere-secrets
          ports:
            - containerPort: 8080
              name: http-health
          readinessProbe:
            tcpSocket:
              port: http-health
            periodSeconds: 10
            timeoutSeconds: 5
          resources:
            requests:
              cpu: 500m
              memory: 2Gi
            limits:
              cpu: "4"
              memory: 2Gi
          volumeMounts:
            - mountPath: /etc/telegraf/telegraf.conf
              name: telegraf-vsphere-volume
              subPath: telegraf.conf
      volumes:
        - name: telegraf-vsphere-volume
          configMap:
            name: telegraf-vsphere-cm
