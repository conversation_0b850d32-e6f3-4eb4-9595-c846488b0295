apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: dre-frosted-azure-test
  namespace: argocd
  labels:
    project: internal
    group: jenkins
spec:
  project: internal
  source:
    repoURL: https://gitlab.ea.com/DRE/kubernetes/clusters/cobra/azure/dre-stockholm/helm-charts/jenkins.git
    path: "."
    targetRevision: 0.9.15
    helm:
      values: |
        instanceName: dre-frosted-azure-test
        production: true
        enableBfaSlack: true
        createAgentListenerService: true
        serviceAccountName: internal
        noPluginsRedeploy: true
        jenkins:
          image: dre-docker-federated.artifacts.ea.com/cobra/jenkins/jenkins-frosted-azure:2.479.3_fa3f874c
          resources:
            requests:
              cpu: 1
              memory: 4Gi
            limits:
              cpu: 2
              memory: 4Gi
          storageSize: 30Gi
        elastic:
          url: http://dre-metrics-eck-es-http.internal.svc.cluster.local:9200
          indexPrefix: frosted
        vault:
          role: cluster-apps-internal
          mainAccountPath: cobra/automation/projects/cobra/accounts/main_account
          appRolePath: cobra/automation/projects/cobra/app_roles/jm-cobra-frosted-azure
          mountPath: kubernetes/cobra/azure/dre-stockholm
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: internal
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
  #   automated:
  #     prune: true
  #     selfHeal: true
