# internal cluster url: http://dre-telemetry-apm-apm-http.internal.svc.cluster.local:8200
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dre-telemetry-apm-ingress
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.middlewares: traefik-https-redirect@kubernetescrd
spec:
  tls:
    - hosts:
        - dre-telemetry-apm.cobra.dre.ea.com
  rules:
    - host: dre-telemetry-apm.cobra.dre.ea.com
      http:
        paths:
          - backend:
              service:
                name: dre-telemetry-apm-apm-http
                port:
                  number: 8200
            path: /
            pathType: Prefix
