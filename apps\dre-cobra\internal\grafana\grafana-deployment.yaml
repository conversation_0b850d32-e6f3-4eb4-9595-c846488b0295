apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: grafana
  name: grafana
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: dre-docker-federated.artifacts.ea.com/cobra/grafana:1.0.602
        imagePullPolicy: "Always"
        resources:
          requests:
            cpu: "2"
            memory: "2Gi"
          limits:
            cpu: "6"
            memory: "8Gi"
        ports:
        - containerPort: 3000
          name: http-grafana
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /robots.txt
            port: 3000
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 30
          successThreshold: 1
          timeoutSeconds: 2
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          tcpSocket:
            port: 3000
          timeoutSeconds: 1
        volumeMounts:
          - mountPath: /var/lib/grafana
            name: grafana-pv
        env:
          - name: CRITERION_LOKI_USERNAME
            value: "loki"
          - name: GF_PLUGINS_ALLOW_LOADING_UNSIGNED_PLUGINS
            value: mullerpeter-databricks-datasource
        envFrom:
          - secretRef:
              name: grafana-secrets
      volumes:
        - name: grafana-pv
          persistentVolumeClaim:
            claimName: grafana-pvc
