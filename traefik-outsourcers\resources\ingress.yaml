kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
  name: traefik-admin
  annotations:
    # Use SSL
    traefik.ingress.kubernetes.io/router.entrypoints: "websecure"
    traefik.ingress.kubernetes.io/router.middlewares: "traefik-https-redirect@kubernetescrd"
    kubernetes.io/ingress.class: traefik-outsourcers
spec:
  tls:
    - hosts:
        - traefik-outsourcers.cobra.dre.ea.com
  rules:
    - host: traefik-outsourcers.cobra.dre.ea.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: traefik-admin-outsourcers
                port:
                  number: 9000