apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dashing-ingress
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.middlewares: traefik-https-redirect@kubernetescrd
spec:
  tls:
    - hosts:
        - dashing.cobra.dre.ea.com
  rules:
    - host: dashing.cobra.dre.ea.com
      http:
        paths:
          - backend:
              service:
                name: dashing-service
                port:
                  name: web
            path: /
            pathType: Prefix
